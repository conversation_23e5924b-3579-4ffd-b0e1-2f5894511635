import { SplashScreen, Stack, Tabs } from 'expo-router';
import React, { useEffect } from 'react';
import "./global.css"; 
import {useFonts} from 'expo-font';




export default function TabLayout() {
const [fontsLoaded] = useFonts({
    'Rubik-Bold': require('./assets/fonts/Rubik-Bold.ttf'),
    'Rubik-ExtraBold': require('./assets/fonts/Rubik-ExtraBold.ttf'),
    'Rubik-Medium': require('./assets/fonts/Rubik-Medium.ttf'),
    'Rubik-Light': require('./assets/fonts/Rubik-Light.ttf'),
    'Rubik-Regular': require('./assets/fonts/Rubik-Regular.ttf'),
    'Rubik-SemiBold': require('./assets/fonts/Rubik-SemiBold.ttf'),
  });
useEffect(() => {
    if (fontsLoaded) {
      // Do something when fonts are loaded
      SplashScreen.hideAsync();
    }
  },[fontsLoaded]);
  if (!fontsLoaded) {
    return null;
  }

  return (
   <Stack/>
  );
}
