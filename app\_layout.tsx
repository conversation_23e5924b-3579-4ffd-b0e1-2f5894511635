import { useFonts } from 'expo-font';
import { SplashScreen, Stack } from 'expo-router';
import "./global.css";
import { useEffect } from 'react';

export default function RootLayout() {
    const [fontsLoaded] = useFonts({
        "rubikBold": require("../assets/fonts/Rubik-Bold.ttf"),
        "rubikExtraBold": require("../assets/fonts/Rubik-ExtraBold.ttf"),
        "rubikMedium": require("../assets/fonts/Rubik-Medium.ttf"),
        "rubikLight": require("../assets/fonts/Rubik-Light.ttf"),
        "rubikRegular": require("../assets/fonts/Rubik-Regular.ttf"),
        "rubikSemiBold": require("../assets/fonts/Rubik-SemiBold.ttf"),
    });
    useEffect(() => {
        if (fontsLoaded) {
            // console.log("Fonts loaded");
            SplashScreen.hideAsync();
        }
    }, [fontsLoaded]);

    if (!fontsLoaded) {
        return null;
    }

  return (
   <Stack screenOptions={{headerShown: false}}/>
  );
}
