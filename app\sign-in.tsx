import images from "@/constants/images";
import icons from "@/constants/icons";
import * as React from "react";
import { Image, ScrollView, Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Link } from "expo-router";

const SingIn = () => {
  const handleLogin= ()=>{};
  return (

    <SafeAreaView className="bg-white h-full">
      <ScrollView contentContainerClassName="h-full">
        <Image
          source={images.onboarding}
          className="w-full h-4/6"
          resizeMode="contain"
        />

        <View className="px-10">
          <Text className="text-base text-center uppercase font-rubik text-black-200">
            Welcome to Restate
          </Text>
          <Text className="text-3xl text-center font-rubikExtraBold text-black-300 mt-2">
            {" "}
            Let's Get You Closer to {"\n"}
            <Text className="text-primary-300">Your Ideal Home</Text>
          </Text>
          <Text className="text-lg text-center font-rubik text-black-200 mt-12">
            {" "}
            Login to Restate With Google
          </Text>
          <TouchableOpacity onPress={handleLogin} className="bg-white border border-black-200 rounded-full w-full py-4 mt-5">
            <View className="flex flex-row items-center justify-center">
                 <Image source={icons.google} className="w-5 h-5" resizeMode="contain"/>
                 <Text className="text-lg font-rubikmedium text-black-300 ml-3">Continue with Google</Text>
            </View>
          </TouchableOpacity >
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default SingIn;
