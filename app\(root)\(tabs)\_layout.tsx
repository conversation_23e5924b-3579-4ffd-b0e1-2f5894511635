import * as React from 'react';
import { Text, View, StyleSheet } from 'react-native';
import { Tabs } from 'expo-router';
const TabIcon =({focused,icon,title}:{focused: boolean,
    icon:any;
    title:string;
}()=>{
    return(
        <View className="items-center justify-center">
            <View className={`w-6 h-6 rounded-full border-2 border-primary-300 ${focused ? "border-primary-300" : "border-transparent"}`}>
                <View className={`w-4 h-4 bg-primary-300 rounded-full ${focused ? "bg-primary-300" : "bg-transparent"}`}/>
            </View>
        </View>
    );
});


const TabsLayout = () => {
  return (
    <Tabs screenOptions={{tabBarShowLabel: false,
        tabBarStyle :{
            backgroundColor: 'white',
            position: "absolute",
            borderTopColor:'#0061FF1A',
            borderTopWidth: 1,
            minHeight: 70,
        },
    }}>
    <Tabs.Screen
    name='index'
    options={{
        tabBarIcon: ({focused}) => (
            <View className="items-center justify-center">
                <View className={`w-6 h-6 rounded-full border-2 border-primary-300 ${focused ? "border-primary-300" : "border-transparent"}`}>
                    <View className={`w-4 h-4 bg-primary-300 rounded-full ${focused ? "bg-primary-300" : "bg-transparent"}`}/>
                </View>
            </View>
        ),
    }}
    />
    </Tabs>
  );
};

export default TabsLayout;
